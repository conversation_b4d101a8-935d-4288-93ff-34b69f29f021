using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB
{
    public partial class Tile : IEquatable<Tile>, IGridActor
    {
        protected static readonly TileParamEnum[] ArmorParams =
        {
            TileParamEnum.ChainLayerCount,
            TileParamEnum.IceLayerCount,
            TileParamEnum.VaseLayerCount,
            TileParamEnum.EggLayerCount,
            TileParamEnum.FlowerPotLayerCount
        };

        public int Id;
        public TileAsset Asset;
        public TileOrigin Origin;
        public TileKinds Kind;
        protected BoosterItem BoostersApplicability = BoosterItem.All;
        public TileSpeciality Speciality { get; protected set; } = TileSpeciality.None;
        public DamageSource AllowedDamageSource { get; protected set; } = DamageSource.AllBase;
        public bool ShouldDelaySimulationOnReaction { get; protected set; }
        public TileState State { get; protected set; }
        public int SortOrder {get; protected set;} = 0;
        private int _hp;
        private List<TileParam> _params;

        public bool IsDead => _hp <= 0;

        public bool IsBoost => Speciality.IsBoost();

        protected const int DefaultHp = 1;

        public bool IsAffectingCellState(DamageSource damageSource, bool tileRemoved = false)
        {
            if (Asset is TileAsset.Simple or TileAsset.DynamiteBox && (tileRemoved
                    ? damageSource == DamageSource.MultiBomb
                    : damageSource != DamageSource.MultiBomb))
            {
                return true;
            }

            return IsBoost && (tileRemoved
                ? damageSource == DamageSource.RemoveColorTiles
                : damageSource != DamageSource.RemoveColorTiles);
        }

        public Tile(int id,
            TileAsset asset,
            TileOrigin origin,
            TileKinds kind,
            List<TileParam> tileParams)
        {
            Id = id;
            Asset = asset;
            Origin = origin;

            Kind = kind;
            _hp = 1;
            _params = tileParams;
        }

        public void SetKind(TileKinds kind)
        {
            Kind = kind;
        }

        /// <summary>
        /// Check if booster can be applied to tile.
        /// </summary>
        /// <param name="boosterItem">Applying booster.</param>
        /// <param name="cell">Applicable cell</param>
        /// <param name="grid">Current grid</param>
        /// <returns>Can booster damage tile.</returns>
        public virtual bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;
            return (BoostersApplicability & boosterItem) != 0;
        }

        public virtual void AddMandatoryParamsTile()
        {
            if (IsAnyOf(TileState.VaseMod))
            {
                if (GetParam(TileParamEnum.VaseLayerCount) <= 0)
                {
                    BDebug.Log(LogCat.Match3, "Spawned vase tile doesn't have vase hp parameter!");
                    SetParam(TileParamEnum.VaseLayerCount, 1);
                }
            }


            var defaultAdjacentHp = DefaultAdjacentHp(State);
            if (defaultAdjacentHp <= 0 || (GetParam(TileParamEnum.AdjacentHp) > 0 &&
                                           GetParam(TileParamEnum.AdjacentHp) <= defaultAdjacentHp)) return;
            BDebug.Log(LogCat.Match3,
                $"Tile '{State}' doesn't have required adjacent parameter. Adding default value {defaultAdjacentHp}");
            SetParam(TileParamEnum.AdjacentHp, defaultAdjacentHp);
        }

        /// <summary>
        /// Tries to apply adjacent damage to the current tile and performs various related actions
        /// Tile specific damage can be calculated by overriding this function
        /// </summary>
        /// <returns>True if the adjacent hit points after the damage are greater than 0, otherwise false.</returns>
        
        public virtual bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0) return false;
            var reducedAdjacentHp = adjacentHp - 1;
            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);

            // Add an action to change the tile parameter (adjacent hit points).
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Coords,
                new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, reducedAdjacentHp) }, hitContext.HitWaitParams));

            // Register that the tile should react to an adjacent hit.
            simulationContext.ReactionHandler.RegisterTileShouldReactOnAdjacentHit(hitContext.MainCell.Coords, 
                hitContext.HitWaitParams, Id, Speciality, State, this, hitContext.Skin, hitContext.CoordsOffset);

            simulationContext.GoalSystem.OnGoalsReceived(new GoalActionRemoveAdjacentLayer(Speciality, 
                hitContext.Coords + hitContext.CoordsOffset));
    
            return reducedAdjacentHp > 0;
        }


        /// <summary>
        /// Apply 1 damage hit to this tile and change it's state accordingly.
        /// </summary>
        /// <returns>Returns type of tile modifier removed on this hit, if any was.</returns>
        public TileState ReduceHitPoints()
        {
            if (IsAnyOf(TileState.SandMod))
            {
                Remove(TileState.Sand);

                // Tile speciality could restore sand mod, then tile itself should be destroyed.
                // This is unique for Sand, because Sand is attachable tile mod that can reside on other tiles and at same time it can be standalone tile.
                // Other attachable tile mods, such as Chain, Vase or IceCube, can not live without other tiles. -VK
                if ((State & TileState.SandMod) != 0)
                {
                    _hp = 0;
                }

                return TileState.SandMod;
            }

            bool vulnerable = IsNoneOf(TileState.Invincible);

            foreach (var armorParam in ArmorParams)
            {
                var value = GetParam(armorParam);
                if (value > 0)
                {
                    var newValue = value - 1;
                    SetParam(armorParam, newValue);

                    if (newValue < 1)
                    {
                        var modState = TileMapper.ParamToModState(armorParam);
                        Remove(modState);

                        if (vulnerable && !Speciality.IsBoost())
                        {
                            if ((modState & TileState.EggMod) != 0)
                            {
                                _hp--;
                            }

                            if ((modState & TileState.FlowerPotMod) != 0)
                            {
                                _hp--;
                            }
                        }

                        return modState;
                    }

                    return TileState.None;
                }
            }

            if (vulnerable)
                _hp--;

            return TileState.None;
        }

        /// <summary>
        /// Handle event of death of the tile before this event will be passed further,
        /// so tile can be resurrected by special mechanics.
        /// </summary>
        /// <returns>Is tile died.</returns>
        /// <remarks>
        /// If tile died, it can be resurrected in this method, and also it will produce reaction to death, which can be handled even if tile will continue to live.
        /// </remarks>
        public bool OnBeforeDieReaction()
        {
            if (_hp <= 0)
            {
                if (CanDieRepeatedly())
                {
                    var shouldDie = false;
                    // Toad used to use RestoresCount param, but now it has been converted to a shared goal, so health
                    // depends on the amount of 'Toad' goals left and no longer on the individual; Toad's health
                    if (HasParam(TileParamEnum.RestoresCount) && Speciality != TileSpeciality.Toad)
                    {
                        var repeats = GetParam(TileParamEnum.RestoresCount);
                        if (repeats > 1)
                        {
                            repeats--;
                            SetParam(TileParamEnum.RestoresCount, repeats);
                        }
                        else
                        {
                            shouldDie = true;
                        }
                    }


                    if (HasParam(TileParamEnum.SquidsCount))
                    {
                        var count = GetParam(TileParamEnum.SquidsCount);
                        shouldDie = count <= 0;
                    }

                    if (HasParam(TileParamEnum.TukTukCount))
                    {
                        var count = GetParam(TileParamEnum.TukTukCount);
                        shouldDie = count <= 0;
                    }

                    if (!shouldDie)
                    {
                        _hp = 1;
                        var adjacentHp = DefaultAdjacentHp(State);
                        if (adjacentHp > 0)
                        {
                            SetParam(TileParamEnum.AdjacentHp, adjacentHp);
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public void MarkAsDead()
        {
            _hp = 0;
        }

        public bool HasParam(TileParamEnum tileParam)
        {
            if (_params == null) return false;

            foreach (var pair in _params)
            {
                if (pair.Param == tileParam)
                {
                    return true;
                }
            }

            return false;
        }

        public int GetParam(TileParamEnum tileParam)
        {
            if (_params == null)
                return 0;

            foreach (var pair in _params)
                if (pair.Param == tileParam)
                    return pair.Value;

            return 0;
        }

        public void SetParam(TileParamEnum tileParam, int value)
        {
            if (_params == null)
            {
                _params = new List<TileParam> { new(tileParam, value) };
            }
            else
            {
                var missing = true;
                for (var i = 0; i < _params.Count; i++)
                {
                    if (_params[i].Param == tileParam)
                    {
                        _params[i] = new TileParam(tileParam, value);
                        missing = false;
                        break;
                    }
                }

                if (missing)
                {
                    _params.Add(new TileParam(tileParam, value));
                }
            }
        }

        /// <summary>
        /// USE IN M3 EDITOR ONLY
        /// </summary>
        public void CleanParams()
        {
            if (_params != null)
            {
                for (var i = 0; i < _params.Count; i++)
                {
                    if (_params[i].Value <= 0)
                    {
                        _params.RemoveAt(i);
                        i--;
                    }
                }

                if (_params.Count == 0)
                {
                    _params = null;
                }
            }
        }

        Coords IGridActor.GetCoords(Grid grid)
        {
            grid.GetTileCoords(this, out var result);
            return result;
        }

        public bool Is(TileState state)
        {
            return (State & state) == state;
        }

        public bool IsAnyOf(TileState state)
        {
            return (State & state) != 0;
        }

        public bool IsNoneOf(TileState state)
        {
            return (State & state) == 0;
        }

        public bool IsBlocker()
        {
            if (State.IsBlockerState())
            {
                return true;
            }

            return Speciality is TileSpeciality.Litter or TileSpeciality.Sticker or TileSpeciality.Pinata
                or TileSpeciality.Frame or TileSpeciality.Gondola;
        }

        /// <summary>
        /// Add mod state.
        /// </summary>
        /// <remarks>
        /// If we add, for example, SandMod,
        /// then this will add Sand state, which contains all associated sub-states.
        /// We can't add Sand directly (which includes SandMod),
        /// because if it's overlapping with any other mod, then error will occur.
        /// (for example, if current state is chain, then it already has ZeroGravity state, which will trigger duplication error)
        /// </remarks>
        public void Add(TileState state)
        {
            State |= state;

            var modFullStates = TileStateExtensions.ModFullStates();
            if (modFullStates.ContainsKey(state))
            {
                State |= modFullStates[state];
            }
        }

        public void Remove(TileState toRemove)
        {
            State &= ~toRemove;

            //Speciality and mod related states are not removable, these lines take care of that
            State |= TileMapper.GetSpecialityDefaultState(Speciality);
            State |= TileMapper.UnwrapMods(State);
        }

        //USE IN EDITOR ONLY
        public List<TileParam> GetParamsDto()
        {
            return _params == null ? null : new List<TileParam>(_params);
        }

        #region Equals

        public bool Equals(Tile other)
        {
            if (ReferenceEquals(null, other)) return false;
            return Id == other.Id;
        }

        public static bool operator ==(Tile tile, Tile other)
        {
            if (ReferenceEquals(other, tile)) return true;
            if (ReferenceEquals(null, tile)) return false;
            return tile.Equals(other);
        }

        public static bool operator !=(Tile tile, Tile other)
        {
            return !(tile == other);
        }

        public override int GetHashCode()
        {
            return Id;
        }

        #endregion

        
        public Tile Clone(TileAsset? asset = null, TileKinds? kind = null)
        {
            var tempParams = _params == null ? null : new List<TileParam>(_params);
            var result = TileFactory.CreateTile(Id, asset ?? Asset, Origin, kind ?? Kind, tempParams);
            result._hp = _hp;
            result.State = State;
            result.Speciality = Speciality;
            result.SortOrder = SortOrder; // Preserve the sort order
            return result;
        }
        
        public Tile CloneIntoUndefined()
        {
            return Clone(TileAsset.Simple, TileKinds.Undefined);
        }

        public bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind)
        {
            if (damageSource == DamageSource.Match && damageTileKind != Kind)
                return false;

            var additionalAllowedDamageSource = TileMapper.GetTileStateAllowedDamageSource(State);
            var totalAllowedDamageSource = AllowedDamageSource | additionalAllowedDamageSource;
            var excludedDamage = TileMapper.GetTileStateExcludedDamageSource(State);
            totalAllowedDamageSource &= ~excludedDamage;

            return CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
        
        protected virtual bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            return (damageSource & totalAllowedDamageSource) != 0;
        }
        
        protected static bool IsSameTileKindDamage(DamageSource damageSource, DamageSource totalAllowedDamageSource)
        {
            return (totalAllowedDamageSource & DamageSource.SameTileKind) != 0
                   && (damageSource & (DamageSource.AdjacentGeneral | DamageSource.RemoveColorTiles)) != 0;
        }

        public bool HasAnyArmor()
        {
            foreach (var armorParam in ArmorParams)
            {
                if (GetParam(armorParam) > 0)
                    return true;
            }

            return false;
        }

        public bool HasMultipleArmorLayer()
        {
            foreach (var armorParam in ArmorParams)
            {
                if (GetParam(armorParam) > 1)
                {
                    return true;
                }
            }
            return false;
        }
        
        public static Vector2 GetCenterPointForIfSquareMechanic(Grid grid, Cell targetCell, Vector2 targetPos, bool shouldTarget)
        {
             targetCell = grid.GetCell(targetCell.Coords);
            
            if (targetCell != null && targetCell.HasTile() && targetCell.HasMultiSizeCellReference() && shouldTarget)
            {
                var sizeX = targetCell.Tile.GetParam(TileParamEnum.SizeX);
                var sizeY = targetCell.Tile.GetParam(TileParamEnum.SizeY);

                if (sizeX == sizeY)
                {
                    var centerX = targetCell.Coords.X + (sizeX - 1) * 0.5f;
                    var centerY = targetCell.Coords.Y + (sizeY - 1) * 0.5f;
                    targetPos = new Vector2(centerX, centerY);
                }
            }

            return targetPos;
        }

        public virtual IEnumerable<(long key, int value)> GetAssistState()
        {
            if (IsAnyOf(TileState.SquidMod))
            {
                var isSingle = GetParam(TileParamEnum.AdjacentHp) > 1;
                var count = GetParam(isSingle ? TileParamEnum.AdjacentHp : TileParamEnum.SquidsCount);
                yield return ((long) GoalType.Squid, count);
            }
            
            if (IsAnyOf(TileState.VaseMod))
            {
                var count = GetParam(TileParamEnum.VaseLayerCount);
                yield return ((long) GoalType.Vase, count);
            }
            
            if (IsAnyOf(TileState.IceCubeMod))
            {
                var count = GetParam(TileParamEnum.IceLayerCount);
                yield return ((long) GoalType.IceCubes, count);
            }

            if (IsAnyOf(TileState.ChainMod))
            {
                var count = GetParam(TileParamEnum.ChainLayerCount);
                yield return ((long) GoalType.Chains, count);
            }
        }
        
        public virtual (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            if (HasParam(TileParamEnum.TileToSpawnFromReaction))
            {
                var tileAsset = (TileAsset)GetParam(TileParamEnum.TileToSpawnFromReaction);
                var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));
                return (newTile, tileAsset, GoalType.None);
            }

            BDebug.LogError(LogCat.Match3, $"Tile {this} has no TileToSpawnFromReaction param, can't create tile from reaction.");
            return (null, TileAsset.Undefined, GoalType.None);
        }
        
        public virtual bool SpawnsCellBackgrounds()
        {
            return false;
        }
        
        public virtual bool SpawnsTileAtRandomPosition()
        {
            return false;
        }
        
        public virtual bool HasNoDieReaction()
        {
            return false;
        }

        protected virtual bool CanDieRepeatedly()
        {
            return false;
        }
        
        public virtual bool DisAllowCellBackgroundSpawn()
        {
            return false;
        }

        private static int DefaultAdjacentHp(TileState state)
        {
            return (state & TileState.SheepMod) != 0 ? 3 : 0;
        }
    }

    public readonly struct TileOrigin
    {
        public readonly Creator CreatorType;
        public readonly Cell Cell;

        public TileOrigin(Creator creatorType, Cell cell)
        {
            CreatorType = creatorType;
            Cell = cell;
        }

        public static TileOrigin Default()
        {
            return new TileOrigin(Creator.None, null);
        }

        public override string ToString()
        {
            return CreatorType.ToString<Creator>() + " " + Cell;
        }

        public string ToConsole()
        {
            if (Cell == null)
            {
                return CreatorType.ToString<Creator>();
            }

            return CreatorType.ToString<Creator>() + " " + Cell.ToConsole();
        }
    }

    public enum Creator
    {
        None,
        Cheat,
        Spawner,
        Match,
        EndGame,
        Loaded,
        SpecialCombination,
        SpecialTile,
        Item,
        LevelEditor,
        ColorBombCombo,
        Propeller
    }

    // Don't change the numbers!!!
    public enum TileAsset
    {
        Undefined = 0,
        Simple = 1,
        BlinkingTile = 2,
        RowBreaker = 3,
        ColumnBreaker = 4,
        Bomb = 5,
        ColorBomb = 6,
        DropItem = 7,
        Litter = 8,
        Sand = 11,
        Pinata = 16,
        Frame = 17,
        ColorCrate = 18,
        Watermelon = 19,
        MoneyBag = 20,
        Penguin = 21,
        Egg = 22,
        Bird = 23,
        Sheep = 24,
        Banana = 25,
        Monkey = 26,
        Skunk = 27,
        Hen = 28,
        Chicken = 29,
        Hive = 30,
        Bee = 31,
        Mole = 32,
        Squid = 33,
        Tnt = 34,
        Toad = 35,
        Propeller = 36,
        Bowling = 37,
        Bush = 38,
        Soda = 39,
        MagicHat = 40,
        Safe = 41,
        FlowerPot = 42,
        IceBar = 43,
        DynamiteBox = 44,
        GiantPinata = 45,
        MetalBar = 46,
        Shelf = 47,
        JellyFish = 48,
        GoldenScarab = 49,
        Gondola = 50,
        TukTuk = 51,
        FireWorks = 52,
        SlotMachine = 53,
        BigMonkey = 54,
        Sticker = 100
    }

    public enum TntTargetType
    {
        Simple,
        BoosterAny,
        BoosterLineBreaker,
        BoosterBomb,
        BoosterBolt,
        DropItem,
        Litter,
        Pinata,
        Sticker,
        ColorCrate,
        Watermelon,
        MoneyBag,
        Egg,
        Bird,
        Sheep,
        Banana,
        Monkey,
        Hen,
        Chicken,
        Bee,
        Mole,
        Squid,
        Grass,
        Ivy,
        Bowling,
        Bush,
        Soda,
        Safe,
        FlowerPot,
        IceBar,
        DynamiteBox,
        GiantPinata,
        MetalBar,
        Shelf,
        JellyFish,
        GoldenScarab,
        FireWorks,
        SlotMachine,
        BigMonkey,
    }

    public enum TileParamEnum : byte
    {
        None = 0,
        AdjacentHp = 1,
        ChainLayerCount = 3,
        IceLayerCount = 4,
        VaseLayerCount = 5,
        EggLayerCount = 6,

        /// <summary>
        /// Restore count for tiles, that should be restored after die. If parameter is missing, then tile will never die.
        /// </summary>
        /// <remarks>
        /// Used for special kind of tiles, that should receive destroy hit multiple times during lifetime,
        /// and which produce some reaction to death event.
        /// Example: Egg, Sheep and Monkey tiles, which spawns tile when receives hit.
        /// </remarks>
        RestoresCount = 7,

        /// <summary>
        /// Flag for beehive when it can't spawn bees anymore.
        /// </summary>
        /// <remarks>
        /// Flag is used to notify view about changed state of beehive, because
        /// the check for this state is too heavy
        /// and involved with Goals manager and grid state, which is hard to access from view layer.
        /// </remarks>
        BeeHiveOutOfBeesFlag = 8,

        /// <summary>
        /// Heal Adjacent HP every turn to specified value
        /// but only if tile didn't receive damage during the turn.
        /// </summary>
        /// <remarks>
        /// Used for Mole tiles that have 2 HP and when they receive 1 hit they will restore after 1 turn without damage.
        /// </remarks>
        PerTurnAutoHealHpValue = 9,

        /// <summary>
        /// Multi-size tiles parameter. Always needs to be together with SizeY param.
        /// </summary>
        SizeX = 10,

        /// <summary>
        /// Multi-size tiles parameter. Always needs to be together with SizeX param.
        /// </summary>
        SizeY = 11,

        /// <summary>
        /// Count of squids inside SquidTile container.
        /// </summary>
        SquidsCount = 12,

        /// <summary>
        /// Bit-masked state of each item inside squid tile.
        /// </summary>
        /// <remarks>
        /// Each state is a 3 bit number, converted to TileKind enum.
        /// there could be 9 different objects max, so there may be used up 27 bits of 32 bits total.
        /// </remarks>
        SquidsState = 13,

        /// <summary>
        /// If bowling box has a curtain
        /// </summary>
        BowlingOpened = 14,

        /// <summary>
        /// If bowling box is empty
        /// </summary>
        BowlingEmpty = 15,
        Skin = 16,

        SodaColors = 17,
        SodaBottlesCount = 18,

        FlowerPotLayerCount = 19,
        MagicHatOutOfRabbitsFlag = 20,
        IceBarOrientation = 21,

        DynamiteBoxColors = 22,
        DynamiteSticksCount = 23,

        MetalBarOrientation = 24,

        ShelfGroupIdentifier = 25,

        JellyFishColor = 27,
        GoldenScarabCount = 28,

        GondolaReached = 29,
        GondolaOrientation = 30,

        TukTukColor = 31,
        TukTukOrientation = 32,
        TukTukCount = 33,
        SlotMachineOutcome = 34,
        TileToSpawnFromReaction = 35,
        TileCreateCountForReaction = 36,
    }
}

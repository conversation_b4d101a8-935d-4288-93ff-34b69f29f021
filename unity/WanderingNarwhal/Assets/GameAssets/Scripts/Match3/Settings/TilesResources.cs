using System;
using System.Collections.Generic;
using BBB;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Renderer;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GameAssets.Scripts.Match3.Settings
{
    public sealed class TilesResources : BbbMonoBehaviour, IContextInitializable
    {
        private struct NormalTileReskin
        {
            public TileKinds Kind;          
            public string Sprite;
        }
        
        private Vector2? _cellSize;
        private Dictionary<CellLayerState, CellViewData> _cellMap;
        private Dictionary<TileKinds, TileViewData> _tileImageMap;
        private Dictionary<TileLayerState, int> _tileOrders;
        

        [Header("Size")]
        [SerializeField]
        private int _maxTileCount;
        [SerializeField]
        private int _maxTileCountVertical;
        [SerializeField]
        private float _tileSizeFactor;
        [SerializeField]
        private float _cellSizeBoxFactor;
        [SerializeField]
        private float _selectionIndicatorSizeFactor;
        [SerializeField]
        private float _tileLayerSpriteRescaleCoef = 1.28f;
        [SerializeField]
        private GameObject _glassImagePrefab;

        [Space(10)]
        [Header("Tile")]
        public GameObject TileContainerPrefab;
        public TileViewData[] TileViewData;
        public ColorCrateViewData[] ColorCrateSprites;

        [Space(10)]
        [Header("Cell")]
        public GameObject CellPrefab;
        public SpriteRenderer CellImagePrefab;
        public CellViewData[] CellData;

        [Space(10)] [Header("Edge")]
        public GameObject EdgePrefab;
        public GameObject HintPrefab;
        private Func<int, int> _skinGetter;
        private IGameEventManager _gameEventManager;
        private GameEventMatch3ManagersCollection _gameEventManagersCollection;
        private IGameEventResourceManager _gameEventResourceManager;
        private IScreensBuilder _screensBuilder;

        private readonly Dictionary<string, NormalTileReskin> _eventReskinMap = new ();

        public int MaxTileCountHorizontal => _maxTileCount;
        public Vector2 CellSize
        {
            get
            {
                if (_cellSize.HasValue)
                    return _cellSize.Value;

                throw new InvalidOperationException("CellSize is not calculated yet (CalculateCellSize method does it)");
            }
        }

        public Vector2 CellSizeBox
        {
            get { return CellSize * _cellSizeBoxFactor; }
        }

        public Vector2 TileSize
        {
            get { return CellSize * _tileSizeFactor; }
        }

        public Vector3 TileLayerSpriteRescale
        {
            get
            {
                var tileSize = TileSize;
                return new Vector3(tileSize.x *_tileLayerSpriteRescaleCoef, tileSize.y * _tileLayerSpriteRescaleCoef, 1f);
            }
        }

        public Vector2 SelectionIndicatorSize
        {
            get { return CellSize * _selectionIndicatorSizeFactor; }
        }

        public GameObject GlassImagePrefab
        {
            get { return _glassImagePrefab; }
        }

        public CellViewData Get(CellLayerState state)
        {
            CellViewData result;
            return _cellMap.TryGetValue(state, out result) ? result : null;
        }

        public TileViewData GetKindData(TileKinds kind)
        {
            return _tileImageMap.TryGetValue(kind, out var tileKindViewData) ? tileKindViewData : _tileImageMap[TileKinds.White];
        }

        public async UniTask<TileViewData> GetAsync(TileKinds kind)
        {
            var result = _tileImageMap[kind];

            if (_eventReskinMap.Count == 0)
            {
                return result;
            }

            foreach (var manager in _gameEventManagersCollection)
            {
                if (!manager.IsNormalTileReskinGameEventType ||
                    manager.ActiveGameEventUid.IsNullOrEmpty())
                {
                    continue;
                }

                if (!_eventReskinMap.TryGetValue(manager.ActiveGameEventUid, out var reskin) ||
                    reskin.Kind != kind ||
                    reskin.Sprite.IsNullOrEmpty())
                {
                    continue;
                }

                var sprite = await _gameEventResourceManager.GetSpriteAsync(manager.ActiveGameEventUid, reskin.Sprite);

                if (sprite != null)
                {
                    return new TileViewData
                    {
                        Sprite = sprite,
                        Color = result.Color,
                        TileKinds = result.TileKinds
                    };
                }
            }

            if (kind == TileKinds.None)
            {
                result.Sprite = null;
            }

            return result;
        }

        public ColorCrateViewData GetDataForColorCrate(TileKinds kind)
        {
            foreach (var coloreCreateViewData in ColorCrateSprites)
            {
                if (coloreCreateViewData.Kind == kind)
                {
                    return coloreCreateViewData;
                }
            }

            if (kind != TileKinds.Undefined)
            {
                UnityEngine.Debug.LogError($"Couldn't find ColorCrateSprites config for kind '{kind}' in m3 resources", this);
            }

            return new ColorCrateViewData()
            {
                Color = Color.white,
            };
        }

        public int Get(TileLayerState layerState)
        {
            if (_tileOrders != null && _tileOrders.ContainsKey(layerState))
            {
                return _tileOrders[layerState];
            }

            UnityEngine.Debug.LogError($"TileLayerState {layerState} is missing in the dict");
            return 0;
        }

        /// <summary>
        /// Gets the default sort order for a tile layer state. This provides fallback values
        /// based on the original ToSortOrder extension method logic.
        /// </summary>
        private static int GetDefaultSortOrderForLayerState(TileLayerState layerState)
        {
            return layerState switch
            {
                TileLayerState.Normal or TileLayerState.Undefined or TileLayerState.Blinking => 10,
                TileLayerState.HorizontalLb or TileLayerState.VerticalLb => 20,
                TileLayerState.MoneyBag or TileLayerState.Penguin or TileLayerState.Egg => 20,
                TileLayerState.Banana or TileLayerState.Chicken or TileLayerState.Bee => 20,
                TileLayerState.FlowerPot or TileLayerState.TukTuk => 20,
                TileLayerState.Sticker or TileLayerState.Litter or TileLayerState.Pinata => 30,
                TileLayerState.Frame or TileLayerState.ColorCrate or TileLayerState.Watermelon or TileLayerState.Mole => 30,
                TileLayerState.Skunk or TileLayerState.Hen or TileLayerState.Hive => 40,
                TileLayerState.Bomb or TileLayerState.Propeller or TileLayerState.ColorBomb or TileLayerState.Squid => 40,
                TileLayerState.DropItem => 50,
                TileLayerState.StealingHatLabel => 55,
                TileLayerState.Vase => 80,
                TileLayerState.Bird => 101,
                TileLayerState.Sheep or TileLayerState.Monkey or TileLayerState.BigMonkey => 102,
                TileLayerState.GameEventLabel => 105,
                TileLayerState.Bowling or TileLayerState.Bush or TileLayerState.Soda or TileLayerState.MagicHat => 109,
                TileLayerState.Safe or TileLayerState.IceBar or TileLayerState.MetalBar or TileLayerState.DynamiteBox => 109,
                TileLayerState.GiantPinata or TileLayerState.Shelf or TileLayerState.JellyFish => 109,
                TileLayerState.GoldenScarab or TileLayerState.FireWorks or TileLayerState.SlotMachine => 109,
                TileLayerState.Animal => 110,
                TileLayerState.Chained => 120,
                TileLayerState.IceCube => 121,
                TileLayerState.Sand => 122,
                TileLayerState.Toad => 123,
                TileLayerState.Gondola => 127,
                _ => 0
            };
        }

        public void InitializeByContext(IContext context)
        {
            _cellMap = new Dictionary<CellLayerState, CellViewData>();
            foreach (var cellData in CellData)
            {
                _cellMap[cellData.State] = cellData;
            }

            _tileImageMap = new Dictionary<TileKinds, TileViewData>();
            foreach (var tileViewData in TileViewData)
            {
                _tileImageMap[tileViewData.TileKinds] = tileViewData;
            }

            _gameEventManagersCollection = context.Resolve<GameEventMatch3ManagersCollection>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _tileOrders = new Dictionary<TileLayerState, int>();
            // Initialize with default orders based on layer states
            foreach (TileLayerState state in Enum.GetValues(typeof(TileLayerState)))
            {
                if (state == TileLayerState.None)
                    continue;

                _tileOrders.Add(state, GetDefaultSortOrderForLayerState(state));
            }
        }

        public void RefreshForLevel(ILevel level)
        {
            RefreshForLevelInternal(level, _gameEventManager, _gameEventManagersCollection, _gameEventResourceManager, _screensBuilder);
        }

        public void RefreshForLevel(ILevel level, IGameEventManager gameEventManager, GameEventMatch3ManagersCollection gameEventManagersCollection, IGameEventResourceManager gameEventResourceManager, IScreensBuilder screensBuilder)
        {
            RefreshForLevelInternal(level, gameEventManager, gameEventManagersCollection, gameEventResourceManager, screensBuilder);
        }

        private void RefreshForLevelInternal(ILevel level, IGameEventManager gameEventManager, GameEventMatch3ManagersCollection gameEventManagersCollection, IGameEventResourceManager gameEventResourceManager, IScreensBuilder screensBuilder)
        {
            _gameEventManager = gameEventManager;
            _gameEventManagersCollection = gameEventManagersCollection;
            _gameEventResourceManager = gameEventResourceManager;
            _screensBuilder = screensBuilder;
            PrepareGameEventNormalTileReskin(level);
        }

        public void AddMilestoneTargetToReskinMap(string gameEventUid, MilestoneTarget milestoneTarget)
        {
            if (milestoneTarget.IsGoalColorTile())
            {
                _eventReskinMap[gameEventUid] = new NormalTileReskin { Kind = milestoneTarget.ToTileKind(), Sprite = "" };
            }
        }

        private void PrepareGameEventNormalTileReskin(ILevel level)
        {
            if (level.Stage >= (int)Stage.Complete) 
                return;

            var possibleTilesReplacements = new List<TileKinds>(2);
            foreach (var manager in _gameEventManagersCollection)
            {
                possibleTilesReplacements.Clear();
                manager.NormalTileSelectedForReskinOnThisLevel = TileKinds.None;
                if (manager.ActiveGameEventUid.IsNullOrEmpty())
                {
                    continue;
                }

                if(!_eventReskinMap.ContainsKey(manager.ActiveGameEventUid))
                    _eventReskinMap[manager.ActiveGameEventUid] = new NormalTileReskin { Kind = TileKinds.None, Sprite = "" };
                var gameEvent = manager.ActiveGameEvent;

                if (!gameEvent.CanShowInScreen(_screensBuilder.CurrentScreenType))
                {
                    continue;
                }

                if (gameEvent is CollectionGameEvent)
                {
                    continue;
                }
                
                var reskinMap = GetNormalTileReskinMap(manager.ActiveGameEventUid, _gameEventResourceManager);
                if (reskinMap == null || reskinMap.Count <= 0) continue;
                foreach (var tileEventSkin in reskinMap)
                {
                    var kind = tileEventSkin.Key;
                    if (level.UsedKinds.Contains(kind) && NotUsedByOthers(manager.ActiveGameEventUid, kind))
                    {
                        possibleTilesReplacements.Add(kind);
                    }
                }
                
                if (possibleTilesReplacements.Count <= 0) 
                    continue;
                
                var selected = possibleTilesReplacements[Random.Range(0, possibleTilesReplacements.Count)];
                _eventReskinMap[manager.ActiveGameEventUid] = new NormalTileReskin { Kind = selected, Sprite = reskinMap[selected] };
                manager.NormalTileSelectedForReskinOnThisLevel = selected;
            }
        }

        private bool NotUsedByOthers(string currentGameEventUid, TileKinds kind)
        {
            foreach (var kvpEventUidReskin in _eventReskinMap)
            {
                if (kvpEventUidReskin.Key == currentGameEventUid)
                {
                    continue;
                }

                if (kvpEventUidReskin.Value.Kind == kind)
                {
                    return false; //used by some other
                }
            }

            return true;
        }

        private Dictionary<TileKinds, string> GetNormalTileReskinMap(string eventUid, IGameEventResourceManager res)
        {
            var settings = res.GetSettings(eventUid);

            if (settings == null)
                return null;
            if (settings.normalTileReskin?.Length > 0)
            {
                var result = new Dictionary<TileKinds, string>();

                foreach (var item in settings.normalTileReskin)
                {
                    result[item.tileKind] = item.sprite;
                }

                return result;
            }
            return null;
        }

        public void CalculateCellSize(IGridController gridController)
        {
            var canvasRect = gridController.RootBoardTransform.rect;
            var cellWidth = Math.Min(canvasRect.width / _maxTileCount, canvasRect.height / _maxTileCountVertical);
            _cellSize = new Vector2(cellWidth, cellWidth);
        }
    }
}

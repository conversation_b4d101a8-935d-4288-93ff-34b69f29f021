using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class TileLayerBase : ITileLayer, IContextInitializable
    {
        protected TilesResources TilesResources;

        public abstract TileLayerState State { get; }

        public int CurrentOrder { get; private set; }

        public virtual void InitializeByContext(IContext context)
        {
            if (TilesResources == null)
            {
                TilesResources = context.Resolve<TilesResources>();
            }

            // Use fallback order from TilesResources, but tiles should set their own SortOrder
            CurrentOrder = TilesResources.Get(State);
        }

        public bool IsRenderer(TileLayerState layerState)
        {
            return IsCondition(layerState);
        }

        public virtual void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            rendererTransform.localRotation = Quaternion.identity;

            // Update CurrentOrder to use the tile's SortOrder if available
            if (tile != null)
            {
                var oldOrder = CurrentOrder;
                CurrentOrder = tile.SortOrder;

                // Apply the sort order difference to all sprite renderers
                var orderDifference = CurrentOrder - oldOrder;
                if (orderDifference != 0)
                {
                    ApplySortOrderToRenderers(rendererTransform.gameObject, orderDifference);

                    // Debug log to verify sort order is being applied
                    UnityEngine.Debug.Log($"Applied sort order {CurrentOrder} to tile {tile.Asset} (difference: {orderDifference})");
                }
            }
        }

        /// <summary>
        /// Applies sort order changes to all renderers in the tile
        /// </summary>
        private void ApplySortOrderToRenderers(GameObject tileObject, int orderDifference)
        {
            // Apply to Canvas components
            var canvas = tileObject.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.sortingOrder += orderDifference;
            }

            // Apply to all SpriteRenderer components recursively
            var spriteRenderers = tileObject.GetComponentsInChildren<SpriteRenderer>(true);
            foreach (var spriteRenderer in spriteRenderers)
            {
                spriteRenderer.sortingOrder += orderDifference;
            }
        }
        protected virtual bool IsCondition(TileLayerState state)
        {
            return (state & State) != 0;
        }
    }
}